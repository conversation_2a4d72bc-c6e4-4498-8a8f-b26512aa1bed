// Simple test to verify export/import functionality
// This can be run in the browser console

// Mock localStorage for testing
const mockStorage = {};
const originalLocalStorage = window.localStorage;

// Override localStorage methods
window.localStorage = {
  getItem: (key) => mockStorage[key] || null,
  setItem: (key, value) => { mockStorage[key] = value; },
  removeItem: (key) => { delete mockStorage[key]; },
  clear: () => { Object.keys(mockStorage).forEach(key => delete mockStorage[key]); }
};

// Test data
const testData = {
  version: '1.0.0',
  exportDate: new Date().toISOString(),
  questions: [
    {
      id: '1',
      title: 'Test Question',
      description: 'This is a test question',
      category: 'CODING'
    }
  ],
  models: [
    {
      id: '1',
      name: 'Test Model',
      provider: 'Test Provider'
    }
  ],
  categories: [
    {
      id: '1',
      name: 'CODING',
      color: '#667eea',
      description: 'Test category'
    }
  ],
  testResults: [
    {
      questionId: '1',
      modelId: '1',
      status: 'passed'
    }
  ]
};

console.log('Testing export/import functionality...');
console.log('Test data:', testData);

// Test validation function
function validateImportData(data) {
  if (!data || typeof data !== 'object') {
    return false;
  }

  // Check required fields
  if (!data.version || !data.exportDate || !Array.isArray(data.questions) || 
      !Array.isArray(data.models) || !Array.isArray(data.categories) || 
      !Array.isArray(data.testResults)) {
    return false;
  }

  // Validate questions structure
  for (const question of data.questions) {
    if (!question.id || !question.title || !question.description || !question.category) {
      return false;
    }
  }

  // Validate models structure
  for (const model of data.models) {
    if (!model.id || !model.name || !model.provider) {
      return false;
    }
  }

  // Validate categories structure
  for (const category of data.categories) {
    if (!category.id || !category.name || !category.color) {
      return false;
    }
  }

  // Validate test results structure
  for (const result of data.testResults) {
    if (!result.questionId || !result.modelId || !result.status ||
        !['passed', 'failed', 'pending'].includes(result.status)) {
      return false;
    }
  }

  return true;
}

// Test validation
const isValid = validateImportData(testData);
console.log('Validation result:', isValid);

if (isValid) {
  console.log('✅ Export/import data structure validation passed!');
} else {
  console.log('❌ Export/import data structure validation failed!');
}

// Test JSON serialization/deserialization
try {
  const jsonStr = JSON.stringify(testData, null, 2);
  const parsedData = JSON.parse(jsonStr);
  const isValidAfterParsing = validateImportData(parsedData);
  
  if (isValidAfterParsing) {
    console.log('✅ JSON serialization/deserialization test passed!');
  } else {
    console.log('❌ JSON serialization/deserialization test failed!');
  }
} catch (error) {
  console.log('❌ JSON serialization/deserialization error:', error);
}

// Restore original localStorage
window.localStorage = originalLocalStorage;

console.log('Export/import functionality test completed!');
