import type { Question, Model, Category, TestResult, ExportData } from '../types';
import { getQuestions, getModels, getCategories, getTestResults, saveQuestions, saveModels, saveCategories, saveTestResults } from './storage';

const EXPORT_VERSION = '1.0.0';

/**
 * Exports all application data to a JSON file
 */
export const exportAllData = (): void => {
  try {
    const exportData: ExportData = {
      version: EXPORT_VERSION,
      exportDate: new Date().toISOString(),
      questions: getQuestions(),
      models: getModels(),
      categories: getCategories(),
      testResults: getTestResults()
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `llm-testing-backup-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // Clean up the URL object
    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('Error exporting data:', error);
    alert('Failed to export data. Please try again.');
  }
};

/**
 * Validates the structure of imported data
 */
const validateImportData = (data: any): data is ExportData => {
  if (!data || typeof data !== 'object') {
    return false;
  }

  // Check required fields
  if (!data.version || !data.exportDate || !Array.isArray(data.questions) || 
      !Array.isArray(data.models) || !Array.isArray(data.categories) || 
      !Array.isArray(data.testResults)) {
    return false;
  }

  // Validate questions structure
  for (const question of data.questions) {
    if (!question.id || !question.title || !question.description || !question.category) {
      return false;
    }
  }

  // Validate models structure
  for (const model of data.models) {
    if (!model.id || !model.name || !model.provider) {
      return false;
    }
  }

  // Validate categories structure
  for (const category of data.categories) {
    if (!category.id || !category.name || !category.color) {
      return false;
    }
  }

  // Validate test results structure
  for (const result of data.testResults) {
    if (!result.questionId || !result.modelId || !result.status ||
        !['passed', 'failed', 'pending'].includes(result.status)) {
      return false;
    }
  }

  return true;
};

/**
 * Imports data from a JSON file and updates the application state
 */
export const importAllData = (file: File): Promise<ExportData> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = (event) => {
      try {
        const jsonStr = event.target?.result as string;
        const importData = JSON.parse(jsonStr);
        
        if (!validateImportData(importData)) {
          reject(new Error('Invalid file format. Please select a valid backup file.'));
          return;
        }

        // Save all data to localStorage
        saveQuestions(importData.questions);
        saveModels(importData.models);
        saveCategories(importData.categories);
        saveTestResults(importData.testResults);
        
        resolve(importData);
      } catch (error) {
        reject(new Error('Failed to parse the backup file. Please ensure it\'s a valid JSON file.'));
      }
    };
    
    reader.onerror = () => {
      reject(new Error('Failed to read the file. Please try again.'));
    };
    
    reader.readAsText(file);
  });
};

/**
 * Gets a summary of the data to be imported for user confirmation
 */
export const getImportSummary = (data: ExportData): string => {
  return `
Import Summary:
• ${data.questions.length} questions
• ${data.models.length} models
• ${data.categories.length} categories
• ${data.testResults.length} test results
• Export date: ${new Date(data.exportDate).toLocaleDateString()}
• Version: ${data.version}

This will replace all current data. Are you sure you want to continue?
  `.trim();
};
