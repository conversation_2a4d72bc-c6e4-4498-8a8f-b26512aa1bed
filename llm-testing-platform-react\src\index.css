/* Animated Background */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
  40%, 43% { transform: translate3d(0, -15px, 0); }
  70% { transform: translate3d(0, -7px, 0); }
  90% { transform: translate3d(0, -2px, 0); }
}

body {
  margin: 0;
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c, #4facfe, #00f2fe);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
  min-height: 100vh;
}

/* Button Animations */
.btn-animated {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  transform: translateZ(0);
}

.btn-animated:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}

.btn-animated:active {
  transform: translateY(0);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn-animated::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.btn-animated:hover::before {
  left: 100%;
}

/* Card Animations */
.card-animated {
  animation: slideIn 0.6s ease-out;
  transition: all 0.3s ease;
}

.card-animated:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* Floating Elements */
.float-animation {
  animation: float 3s ease-in-out infinite;
}

/* Pulse Animation */
.pulse-animation {
  animation: pulse 2s ease-in-out infinite;
}

/* Shimmer Effect */
.shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

/* Bounce Animation */
.bounce-animation {
  animation: bounce 2s infinite;
}

/* Glass Effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Gradient Text */
.gradient-text {
  background: linear-gradient(45deg, #667eea, #764ba2, #f093fb);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: gradientShift 3s ease infinite;
  background-size: 200% 200%;
}

/* Status Indicators with Animation */
.status-passed {
  background: linear-gradient(45deg, #10b981, #34d399);
  animation: pulse 2s ease-in-out infinite;
}

.status-failed {
  background: linear-gradient(45deg, #ef4444, #f87171);
  animation: pulse 2s ease-in-out infinite;
}

.status-pending {
  background: linear-gradient(45deg, #f59e0b, #fbbf24);
  animation: pulse 2s ease-in-out infinite;
}

/* Prevent horizontal scrolling */
* {
  box-sizing: border-box;
}

html, body {
  overflow-x: hidden;
  max-width: 100vw;
}

/* Table responsive design */
.table-container {
  width: 100%;
  overflow-x: auto;
  max-width: 100%;
  /* Add smooth scrolling */
  scroll-behavior: smooth;
  /* Add scrollbar styling */
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
}

.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Testing Grid Table Layout */
.testing-grid-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  min-width: calc(var(--questions-width, 400px) + 140px * var(--model-count, 1));
}

.testing-grid-table td, .testing-grid-table th {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  border: none;
  outline: none;
  vertical-align: top;
}

.testing-grid-table tr {
  border: none;
  outline: none;
}

/* Questions column - dynamic width with proper text wrapping */
.questions-column {
  width: var(--questions-width, 400px);
  min-width: var(--questions-width, 400px);
  max-width: var(--questions-width, 400px);
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  position: relative;
}

/* Model columns - fixed minimum width */
.model-column {
  min-width: 140px;
  width: 140px;
  max-width: 140px;
}

/* Removed scroll indicator to fix hover line issue */

/* Resize handle styling */
.questions-column .resize-handle {
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  cursor: col-resize;
  background: rgba(255, 255, 255, 0.2);
  transition: background-color 0.2s ease;
  z-index: 10;
}

.questions-column .resize-handle:hover {
  background: rgba(255, 255, 255, 0.4);
}

.questions-column .resize-handle.resizing {
  background: rgba(255, 255, 255, 0.6);
}

/* Add visual feedback during resize */
body.resizing-column {
  cursor: col-resize !important;
  user-select: none !important;
}

body.resizing-column * {
  cursor: col-resize !important;
  user-select: none !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .testing-grid-table {
    table-layout: auto;
  }

  .questions-column {
    width: var(--questions-width, 300px);
    min-width: var(--questions-width, 300px);
    max-width: var(--questions-width, 300px);
  }

  .model-column {
    min-width: 120px;
    width: 120px;
    max-width: 120px;
  }

  /* Hide resize handle on mobile for better UX */
  .questions-column .resize-handle {
    display: none;
  }
}

/* Ensure text wraps properly */
.break-words {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
}

/* Force text to wrap anywhere if needed */
.overflow-wrap-anywhere {
  overflow-wrap: anywhere;
  word-break: break-word;
}

/* Additional table cell text wrapping */
.testing-grid-table .questions-column div {
  max-width: 100%;
  overflow-wrap: anywhere;
  word-break: break-word;
}
