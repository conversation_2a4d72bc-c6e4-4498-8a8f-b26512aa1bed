import React, { useState, useEffect, useRef } from 'react';
import type { Question, Model, TestResult, ModelScore } from './types';
import { getQuestions, getModels, getTestResults, saveTestResults, getCategories } from './utils/storage';
import { exportAllData, importAllData, getImportSummary } from './utils/dataExport';
import QuestionManager from './components/QuestionManager';
import ModelManager from './components/ModelManager';
import TestingGrid from './components/TestingGrid';
import Leaderboard from './components/Leaderboard';
import CategoryManager from './components/CategoryManager';

function App() {
  const [questions, setQuestions] = useState<Question[]>([]);
  const [models, setModels] = useState<Model[]>([]);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [activeTab, setActiveTab] = useState<'testing' | 'questions' | 'models' | 'categories' | 'leaderboard'>('testing');
  const [refreshKey, setRefreshKey] = useState<number>(0);
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    setQuestions(getQuestions());
    setModels(getModels());
    setTestResults(getTestResults());
  }, []);

  const updateTestResult = (questionId: string, modelId: string, status: 'passed' | 'failed' | 'pending') => {
    const newResults = testResults.filter(r => !(r.questionId === questionId && r.modelId === modelId));
    newResults.push({ questionId, modelId, status });
    setTestResults(newResults);
    saveTestResults(newResults);
  };

  const calculateLeaderboard = (): ModelScore[] => {
    // Filter questions by selected category
    const filteredQuestions = selectedCategory
      ? questions.filter(q => q.category === selectedCategory)
      : questions;

    // Filter models by selected provider
    const filteredModels = selectedProvider
      ? models.filter(m => m.provider === selectedProvider)
      : models;

    return filteredModels.map(model => {
      // Only consider test results for filtered questions
      const relevantResults = testResults.filter(r =>
        r.modelId === model.id &&
        filteredQuestions.some(q => q.id === r.questionId)
      );

      const passed = relevantResults.filter(r => r.status === 'passed').length;
      const failed = relevantResults.filter(r => r.status === 'failed').length;
      const pending = filteredQuestions.length - passed - failed;

      return {
        modelId: model.id,
        modelName: model.name,
        provider: model.provider,
        icon: model.icon,
        imageUrl: model.imageUrl,
        totalQuestions: filteredQuestions.length,
        passedQuestions: passed,
        failedQuestions: failed,
        pendingQuestions: pending,
        passRate: filteredQuestions.length > 0 ? (passed / filteredQuestions.length) * 100 : 0
      };
    }).sort((a, b) => b.passRate - a.passRate);
  };

  const getAvailableProviders = (): string[] => {
    const providers = [...new Set(models.map(model => model.provider))];
    return providers.sort();
  };

  const handleExportData = () => {
    try {
      exportAllData();
      alert('Data exported successfully! Check your downloads folder.');
    } catch (error) {
      console.error('Export failed:', error);
      alert('Failed to export data. Please try again.');
    }
  };

  const handleImportData = () => {
    fileInputRef.current?.click();
  };

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const importData = await importAllData(file);
      const summary = getImportSummary(importData);

      if (confirm(summary)) {
        // Refresh the state with imported data
        setQuestions(getQuestions());
        setModels(getModels());
        setTestResults(getTestResults());
        setRefreshKey(prev => prev + 1); // Force re-render of components that depend on localStorage

        alert('Data imported successfully!');
      }
    } catch (error) {
      console.error('Import failed:', error);
      alert(error instanceof Error ? error.message : 'Failed to import data. Please try again.');
    }

    // Reset file input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <div className="min-h-screen">
      {/* Animated Header */}
      <div className="glass-effect text-white p-6 shadow-2xl">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-4xl font-bold mb-6 gradient-text float-animation">
            🚀 AI Test v2 🤖
          </h1>
          <nav className="flex flex-wrap gap-3">
            <button
              onClick={() => setActiveTab('testing')}
              className={`btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'testing'
                  ? 'bg-gradient-to-r from-purple-600 to-blue-600 shadow-lg scale-105'
                  : 'bg-gradient-to-r from-purple-500 to-blue-500 hover:from-purple-600 hover:to-blue-600'
              }`}
            >
              🧪 Testing Grid
            </button>
            <button
              onClick={() => setActiveTab('questions')}
              className={`btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'questions'
                  ? 'bg-gradient-to-r from-green-600 to-teal-600 shadow-lg scale-105'
                  : 'bg-gradient-to-r from-green-500 to-teal-500 hover:from-green-600 hover:to-teal-600'
              }`}
            >
              ❓ Questions
            </button>
            <button
              onClick={() => setActiveTab('models')}
              className={`btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'models'
                  ? 'bg-gradient-to-r from-orange-600 to-red-600 shadow-lg scale-105'
                  : 'bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600'
              }`}
            >
              🤖 Models
            </button>
            <button
              onClick={() => setActiveTab('categories')}
              className={`btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'categories'
                  ? 'bg-gradient-to-r from-pink-600 to-purple-600 shadow-lg scale-105'
                  : 'bg-gradient-to-r from-pink-500 to-purple-500 hover:from-pink-600 hover:to-purple-600'
              }`}
            >
              🏷️ Categories
            </button>
            <button
              onClick={() => setActiveTab('leaderboard')}
              className={`btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'leaderboard'
                  ? 'bg-gradient-to-r from-yellow-600 to-orange-600 shadow-lg scale-105'
                  : 'bg-gradient-to-r from-yellow-500 to-orange-500 hover:from-yellow-600 hover:to-orange-600'
              }`}
            >
              🏆 Leaderboard
            </button>

            {/* Export/Import Section */}
            <div className="flex gap-3 ml-auto">
              <button
                onClick={handleExportData}
                className="btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 bg-gradient-to-r from-emerald-500 to-teal-500 hover:from-emerald-600 hover:to-teal-600"
                title="Export all data (questions, models, categories, test results)"
              >
                📤 Export Data
              </button>
              <button
                onClick={handleImportData}
                className="btn-animated px-6 py-3 rounded-xl font-semibold transition-all duration-300 bg-gradient-to-r from-indigo-500 to-purple-500 hover:from-indigo-600 hover:to-purple-600"
                title="Import data from backup file"
              >
                📥 Import Data
              </button>
            </div>
          </nav>

          {/* Hidden file input for import */}
          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
          />
        </div>
      </div>

      {/* Main Content with Animation */}
      <div className={activeTab === 'testing' ? 'w-full px-2 py-4' : 'max-w-7xl mx-auto p-6'}>
        <div className="card-animated">
          {activeTab === 'testing' && (
            <TestingGrid
              questions={questions}
              models={models}
              testResults={testResults}
              onUpdateResult={updateTestResult}
            />
          )}
          {activeTab === 'questions' && (
            <QuestionManager
              key={refreshKey}
              questions={questions}
              onQuestionsChange={setQuestions}
            />
          )}
          {activeTab === 'models' && (
            <ModelManager
              models={models}
              onModelsChange={setModels}
            />
          )}
          {activeTab === 'categories' && (
            <CategoryManager key={refreshKey} />
          )}
          {activeTab === 'leaderboard' && (
            <Leaderboard
              scores={calculateLeaderboard()}
              onProviderFilterChange={setSelectedProvider}
              onCategoryFilterChange={setSelectedCategory}
              availableProviders={getAvailableProviders()}
              selectedProvider={selectedProvider}
              selectedCategory={selectedCategory}
            />
          )}
        </div>
      </div>
    </div>
  );
}

export default App;
